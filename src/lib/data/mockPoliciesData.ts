import type { 
    Policy, 
    Claim, 
    PolicyStatistics, 
    CustomerPoliciesData,
    PolicyProduct,
    PolicyBeneficiary,
    PolicyDocument,
    ClaimDocument,
    ClaimStatusHistory
} from '$lib/types/customer';

// Mock Policy Products
export const mockPolicyProducts: PolicyProduct[] = [
    {
        id: 1,
        name: 'Life Protection Plus',
        product_type: 'LIFE',
        description: 'Comprehensive life insurance with investment benefits',
        category: 'Term Life',
        provider: 'AIBL Insurance',
        coverage_details: [
            'Death benefit coverage',
            'Terminal illness benefit',
            'Accidental death benefit (double coverage)',
            'Waiver of premium on disability'
        ],
        exclusions: ['Suicide within first 2 years', 'War and terrorism', 'Extreme sports']
    },
    {
        id: 2,
        name: 'Health Shield Premium',
        product_type: 'HEALTH',
        description: 'Comprehensive health insurance with worldwide coverage',
        category: 'Medical Insurance',
        provider: 'AIBL Insurance',
        coverage_details: [
            'Inpatient and outpatient coverage',
            'Emergency medical evacuation',
            'Prescription drug coverage',
            'Preventive care benefits'
        ],
        exclusions: ['Pre-existing conditions (first 12 months)', 'Cosmetic surgery', 'Experimental treatments']
    },
    {
        id: 3,
        name: 'Auto Complete Coverage',
        product_type: 'AUTO',
        description: 'Full coverage auto insurance with roadside assistance',
        category: 'Vehicle Insurance',
        provider: 'AIBL Insurance',
        coverage_details: [
            'Collision and comprehensive coverage',
            'Liability protection',
            '24/7 roadside assistance',
            'Rental car coverage'
        ],
        exclusions: ['Racing or competition use', 'Commercial use', 'Intentional damage']
    },
    {
        id: 4,
        name: 'Home Guardian',
        product_type: 'PROPERTY',
        description: 'Complete home and property protection',
        category: 'Property Insurance',
        provider: 'AIBL Insurance',
        coverage_details: [
            'Dwelling and structure coverage',
            'Personal property protection',
            'Liability coverage',
            'Additional living expenses'
        ],
        exclusions: ['Flood damage', 'Earthquake damage', 'Normal wear and tear']
    }
];

// Mock Beneficiaries
export const mockBeneficiaries: PolicyBeneficiary[] = [
    {
        id: 1,
        name: 'สมหญิง ใจดี',
        relationship: 'Spouse',
        percentage: 60,
        contact_info: {
            phone: '+66812345679',
            email: '<EMAIL>'
        }
    },
    {
        id: 2,
        name: 'สมชาติ ใจดี',
        relationship: 'Child',
        percentage: 40,
        contact_info: {
            phone: '+66812345680'
        }
    }
];

// Mock Policy Documents
export const mockPolicyDocuments: PolicyDocument[] = [
    {
        id: 1,
        document_type: 'POLICY_CERTIFICATE',
        file_name: 'policy_certificate_LP001.pdf',
        file_size: 245760,
        uploaded_date: '2024-01-15T10:30:00Z',
        uploaded_by: 'System'
    },
    {
        id: 2,
        document_type: 'TERMS_CONDITIONS',
        file_name: 'terms_conditions_LP001.pdf',
        file_size: 512000,
        uploaded_date: '2024-01-15T10:30:00Z',
        uploaded_by: 'System'
    }
];

// Mock Policies
export const mockPolicies: Policy[] = [
    {
        id: 1,
        policy_number: 'LP001-2024-001',
        customer_id: 1,
        product: mockPolicyProducts[0],
        policy_status: 'ACTIVE',
        issue_date: '2024-01-15T00:00:00Z',
        start_date: '2024-02-01T00:00:00Z',
        end_date: '2034-01-31T23:59:59Z',
        renewal_date: '2025-02-01T00:00:00Z',
        premium_amount: 25000,
        coverage_amount: 2000000,
        currency: 'THB',
        payment_frequency: 'ANNUAL',
        next_payment_date: '2025-02-01T00:00:00Z',
        beneficiaries: mockBeneficiaries,
        documents: mockPolicyDocuments,
        total_claims: 0,
        active_claims: 0,
        total_claims_amount: 0,
        created_on: '2024-01-15T10:30:00Z',
        updated_on: '2024-01-15T10:30:00Z',
        notes: 'Premium customer with excellent payment history'
    },
    {
        id: 2,
        policy_number: 'HS002-2024-001',
        customer_id: 1,
        product: mockPolicyProducts[1],
        policy_status: 'ACTIVE',
        issue_date: '2024-03-01T00:00:00Z',
        start_date: '2024-03-15T00:00:00Z',
        end_date: '2025-03-14T23:59:59Z',
        renewal_date: '2025-03-15T00:00:00Z',
        premium_amount: 18000,
        coverage_amount: 1000000,
        currency: 'THB',
        payment_frequency: 'ANNUAL',
        next_payment_date: '2025-03-15T00:00:00Z',
        documents: [
            {
                id: 3,
                document_type: 'POLICY_CERTIFICATE',
                file_name: 'health_policy_HS002.pdf',
                file_size: 198400,
                uploaded_date: '2024-03-01T14:20:00Z',
                uploaded_by: 'System'
            }
        ],
        total_claims: 2,
        active_claims: 1,
        total_claims_amount: 45000,
        created_on: '2024-03-01T14:20:00Z',
        updated_on: '2024-08-15T09:15:00Z'
    },
    {
        id: 3,
        policy_number: 'AC003-2023-001',
        customer_id: 1,
        product: mockPolicyProducts[2],
        policy_status: 'ACTIVE',
        issue_date: '2023-06-01T00:00:00Z',
        start_date: '2023-06-15T00:00:00Z',
        end_date: '2024-06-14T23:59:59Z',
        renewal_date: '2024-06-15T00:00:00Z',
        premium_amount: 12000,
        coverage_amount: 800000,
        currency: 'THB',
        payment_frequency: 'ANNUAL',
        next_payment_date: '2024-06-15T00:00:00Z',
        total_claims: 1,
        active_claims: 0,
        total_claims_amount: 15000,
        created_on: '2023-06-01T11:45:00Z',
        updated_on: '2024-02-20T16:30:00Z'
    }
];

// Mock Claim Status History
export const mockClaimStatusHistory: ClaimStatusHistory[] = [
    {
        id: 1,
        claim_id: 1,
        status: 'SUBMITTED',
        status_date: '2024-07-15T09:30:00Z',
        updated_by: 'Customer Portal',
        notes: 'Claim submitted online with all required documents'
    },
    {
        id: 2,
        claim_id: 1,
        status: 'UNDER_REVIEW',
        status_date: '2024-07-16T10:15:00Z',
        updated_by: 'Claims Adjuster - John Smith',
        notes: 'Initial review completed, medical records requested'
    },
    {
        id: 3,
        claim_id: 1,
        status: 'APPROVED',
        status_date: '2024-08-01T14:20:00Z',
        updated_by: 'Claims Manager - Sarah Johnson',
        notes: 'Claim approved for full amount after medical review'
    }
];

// Mock Claim Documents
export const mockClaimDocuments: ClaimDocument[] = [
    {
        id: 1,
        claim_id: 1,
        document_type: 'MEDICAL_REPORT',
        file_name: 'medical_report_20240715.pdf',
        file_size: 156800,
        uploaded_date: '2024-07-15T09:30:00Z',
        uploaded_by: 'Customer',
        description: 'Hospital medical report for outpatient treatment'
    },
    {
        id: 2,
        claim_id: 1,
        document_type: 'RECEIPT',
        file_name: 'hospital_receipt_20240715.pdf',
        file_size: 89600,
        uploaded_date: '2024-07-15T09:32:00Z',
        uploaded_by: 'Customer',
        description: 'Official hospital payment receipt'
    }
];

// Mock Claims
export const mockClaims: Claim[] = [
    {
        id: 1,
        claim_number: 'CLM-HS002-2024-001',
        policy_id: 2,
        policy_number: 'HS002-2024-001',
        customer_id: 1,
        claim_type: 'MEDICAL',
        claim_status: 'APPROVED',
        incident_date: '2024-07-10T00:00:00Z',
        reported_date: '2024-07-15T09:30:00Z',
        claimed_amount: 25000,
        approved_amount: 25000,
        paid_amount: 25000,
        currency: 'THB',
        description: 'Outpatient medical treatment for acute gastritis',
        incident_location: 'Bangkok Hospital',
        cause_of_claim: 'Sudden illness requiring immediate medical attention',
        assigned_adjuster: 'John Smith',
        settlement_date: '2024-08-05T00:00:00Z',
        documents: mockClaimDocuments,
        status_history: mockClaimStatusHistory,
        created_on: '2024-07-15T09:30:00Z',
        updated_on: '2024-08-05T16:45:00Z',
        notes: 'Straightforward medical claim, all documentation provided'
    },
    {
        id: 2,
        claim_number: 'CLM-HS002-2024-002',
        policy_id: 2,
        policy_number: 'HS002-2024-001',
        customer_id: 1,
        claim_type: 'HOSPITALIZATION',
        claim_status: 'UNDER_REVIEW',
        incident_date: '2024-08-10T00:00:00Z',
        reported_date: '2024-08-12T14:20:00Z',
        claimed_amount: 20000,
        currency: 'THB',
        description: 'Emergency hospitalization for food poisoning',
        incident_location: 'Bumrungrad Hospital',
        cause_of_claim: 'Severe food poisoning requiring 2-day hospitalization',
        assigned_adjuster: 'Sarah Johnson',
        estimated_settlement_date: '2024-08-25T00:00:00Z',
        documents: [
            {
                id: 3,
                claim_id: 2,
                document_type: 'MEDICAL_REPORT',
                file_name: 'emergency_report_20240810.pdf',
                file_size: 234560,
                uploaded_date: '2024-08-12T14:25:00Z',
                uploaded_by: 'Customer',
                description: 'Emergency department admission report'
            }
        ],
        status_history: [
            {
                id: 4,
                claim_id: 2,
                status: 'SUBMITTED',
                status_date: '2024-08-12T14:20:00Z',
                updated_by: 'Customer Portal',
                notes: 'Emergency claim submitted with initial documentation'
            },
            {
                id: 5,
                claim_id: 2,
                status: 'UNDER_REVIEW',
                status_date: '2024-08-13T09:00:00Z',
                updated_by: 'Claims Adjuster - Sarah Johnson',
                notes: 'Review in progress, additional medical records requested'
            }
        ],
        created_on: '2024-08-12T14:20:00Z',
        updated_on: '2024-08-13T09:00:00Z'
    },
    {
        id: 3,
        claim_number: 'CLM-AC003-2024-001',
        policy_id: 3,
        policy_number: 'AC003-2023-001',
        customer_id: 1,
        claim_type: 'ACCIDENT',
        claim_status: 'PAID',
        incident_date: '2024-01-20T00:00:00Z',
        reported_date: '2024-01-22T10:15:00Z',
        claimed_amount: 15000,
        approved_amount: 15000,
        paid_amount: 15000,
        currency: 'THB',
        description: 'Minor vehicle collision damage',
        incident_location: 'Sukhumvit Road, Bangkok',
        cause_of_claim: 'Rear-end collision at traffic light',
        assigned_adjuster: 'Mike Wilson',
        settlement_date: '2024-02-15T00:00:00Z',
        documents: [
            {
                id: 4,
                claim_id: 3,
                document_type: 'POLICE_REPORT',
                file_name: 'police_report_20240120.pdf',
                file_size: 178900,
                uploaded_date: '2024-01-22T10:20:00Z',
                uploaded_by: 'Customer',
                description: 'Official police accident report'
            },
            {
                id: 5,
                claim_id: 3,
                document_type: 'PHOTO_EVIDENCE',
                file_name: 'vehicle_damage_photos.zip',
                file_size: 2456789,
                uploaded_date: '2024-01-22T10:25:00Z',
                uploaded_by: 'Customer',
                description: 'Photos of vehicle damage from multiple angles'
            }
        ],
        status_history: [
            {
                id: 6,
                claim_id: 3,
                status: 'SUBMITTED',
                status_date: '2024-01-22T10:15:00Z',
                updated_by: 'Customer Portal',
                notes: 'Accident claim submitted with police report'
            },
            {
                id: 7,
                claim_id: 3,
                status: 'APPROVED',
                status_date: '2024-02-05T11:30:00Z',
                updated_by: 'Claims Manager - Mike Wilson',
                notes: 'Claim approved after damage assessment'
            },
            {
                id: 8,
                claim_id: 3,
                status: 'PAID',
                status_date: '2024-02-15T09:45:00Z',
                updated_by: 'Finance Department',
                notes: 'Payment processed to customer account'
            }
        ],
        created_on: '2024-01-22T10:15:00Z',
        updated_on: '2024-02-15T09:45:00Z',
        notes: 'Clean claim with no complications'
    }
];

// Mock Policy Statistics
export const mockPolicyStatistics: PolicyStatistics = {
    total_policies: 3,
    active_policies: 3,
    expired_policies: 0,
    pending_policies: 0,
    cancelled_policies: 0,
    waiting_period_policies: 0,
    nearly_expired_policies: 1, // Auto policy expiring soon

    total_premium_amount: 55000,
    total_coverage_amount: 3800000,
    average_premium: 18333,

    total_claims: 3,
    active_claims: 1,
    approved_claims: 2,
    rejected_claims: 0,
    total_claims_amount: 60000,
    total_paid_amount: 40000,

    policy_type_breakdown: {
        'LIFE': 1,
        'HEALTH': 1,
        'AUTO': 1,
        'PROPERTY': 0,
        'TRAVEL': 0,
        'DISABILITY': 0,
        'CRITICAL_ILLNESS': 0
    },

    recent_policies: 0,
    recent_claims: 1
};

// Complete Customer Policies Data
export const mockCustomerPoliciesData: CustomerPoliciesData = {
    customer_id: 1,
    customer_name: 'สมชาย ใจดี',
    customer_email: '<EMAIL>',
    policies: mockPolicies,
    claims: mockClaims,
    statistics: mockPolicyStatistics,
    last_updated: '2024-08-20T10:30:00Z'
};

// Helper function to get policies by customer ID
export function getPoliciesByCustomerId(customerId: number): CustomerPoliciesData {
    // For now, return the same mock data for any customer ID
    // In a real implementation, this would filter by customer ID
    return {
        ...mockCustomerPoliciesData,
        customer_id: customerId
    };
}

// Helper function to get policy by ID
export function getPolicyById(policyId: number): Policy | undefined {
    return mockPolicies.find(policy => policy.id === policyId);
}

// Helper function to get claims by policy ID
export function getClaimsByPolicyId(policyId: number): Claim[] {
    return mockClaims.filter(claim => claim.policy_id === policyId);
}

// Helper function to get claim by ID
export function getClaimById(claimId: number): Claim | undefined {
    return mockClaims.find(claim => claim.id === claimId);
}
