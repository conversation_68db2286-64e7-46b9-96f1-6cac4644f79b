<script lang="ts">
	import { t } from '$lib/stores/i18n';
	import type { Customer } from '$lib/types/customer';
	import InformationTab from './tabs/InformationTab.svelte';
	import RequestSummaryTab from './tabs/RequestSummaryTab.svelte';
	import TimelineTab from './tabs/TimelineTab.svelte';
	import PoliciesTab from './tabs/PoliciesTab.svelte';
	import AIGuidanceTab from './tabs/AIGuidanceTab.svelte';

	export let customer: Customer;
	// console.log(customer)
	export let platformId: number | null = null;
	export let access_token: string;
	export let hideAITab: boolean = false;
	export let ticketId: number | null = null;
	export let hideEditButton: boolean = false;
	export let hideAssignmentsHistory: boolean = false;

	let activeTab = 'information';
	
	const baseTabs = [
		{ id: 'information', label: 'Information', key: 'information', component: InformationTab },
		{ id: 'policies', label: 'Policies', key: 'policies', component: PoliciesTab },
		{ id: 'request', label: 'Summary', key: 'summary', component: RequestSummaryTab },
		// { id: 'timeline', label: 'Timeline', key: 'timeline', component: TimelineTab }, //TO DO LATER FOR BVTPA
	];

	const aiTab = { id: 'ai', label: 'AI Assistant', key: 'ai_assistant', component: AIGuidanceTab };
	const tabs = hideAITab ? baseTabs : [...baseTabs, aiTab];
</script>

<div id="customer-info-customer-info-panel" class="h-full w-full flex flex-col" data-testid="customer-info-panel">
	<!-- Tab Navigation -->
	<div id="customer-info-customer-info-tabs-container" class="bg-white border-b border-gray-200 flex-shrink-0">
		<nav id="customer-info-customer-info-tabs" class="flex w-full">
			{#each tabs as tab}
				<button
					id="customer-info-customer-tab-{tab.id}"
					on:click={() => activeTab = tab.id}
					class="flex-1 px-4 py-4 text-sm font-medium border-b-2 transition-colors whitespace-nowrap text-center
						{activeTab === tab.id
							? 'border-black text-black bg-white'
							: 'border-transparent text-gray-500 hover:text-gray-700'}"
					data-testid="customer-tab-{tab.id}"
					aria-selected={activeTab === tab.id}
					role="tab"
				>
					<!-- {tab.label} -->
					{t(tab.key)}
				</button>
			{/each}
		</nav>
	</div>

	<!-- Tab Content -->
	<div id="customer-info-customer-info-content" class="flex-1 overflow-y-auto bg-gray-50 w-full" data-testid="customer-info-content">
		<div class="w-full h-full">
			{#each tabs as tab}
				{#if activeTab === tab.id}
					<div id="customer-info-customer-tab-content-{tab.id}" data-testid="customer-tab-content-{tab.id}" role="tabpanel" aria-labelledby="customer-info-customer-tab-{tab.id}">
						<svelte:component this={tab.component} {customer} {access_token} {platformId} {ticketId} {hideEditButton} {hideAssignmentsHistory} />
					</div>
				{/if}
			{/each}
		</div>
	</div>
</div>