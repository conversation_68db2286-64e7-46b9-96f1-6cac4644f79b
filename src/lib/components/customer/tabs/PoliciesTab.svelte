<!--
	@component PoliciesTab

	A comprehensive policies and claims management component for the CustomerInfoPanel.
	Provides viewing, filtering, and detailed information about customer insurance policies and claims.

	Features:
	- Policy and claims overview with summary statistics
	- Advanced filtering by status, type, and search
	- Detailed policy and claim information modals
	- Real-time data updates via polling
	- Full accessibility and internationalization support

	@example
	```svelte
	<PoliciesTab
		{customer}
		{access_token}
		platformId={1}
		ticketId={123}
		hideEditButton={false}
		hideAssignmentsHistory={false}
	/>
	```
-->
<script lang="ts">
	import type { Customer, CustomerPoliciesData, Policy, Claim, PolicyFilters, ClaimFilters } from '$lib/types/customer';
	import type { PollingConfig } from '$lib/types/polling';
	import { onMount, onDestroy } from 'svelte';
	import { t, language } from '$lib/stores/i18n';
	import { get } from 'svelte/store';
	import { PollingService } from '$lib/services/pollingService';
	import { 
		Card,
		Button,
		Input,
		Select,
		Badge,
		Modal,
		Timeline,
		TimelineItem,
		Spinner
	} from 'flowbite-svelte';
	import {
		TicketSolid,
		ClipboardListSolid,
		CalendarMonthSolid,
		ClockSolid,
		CloseCircleSolid,
		SearchOutline,
		FilterSolid,
		EyeOutline,
		NewspaperOutline,
		ExclamationCircleOutline
	} from 'flowbite-svelte-icons';

	// Import mock data and API services
	import { getPoliciesByCustomerId } from '$lib/data/mockPoliciesData';
	import { services } from '$src/lib/api/features';

	// Props following existing tab component patterns
	/** Customer data object containing basic customer information */
	export let customer: Customer;
	/** Platform identifier for multi-platform support (optional) */
	export let platformId: number | null = null;
	/** Authentication token for API requests */
	export let access_token: string;
	/** Associated ticket ID for context (optional) */
	export let ticketId: number | null = null;
	/** Hide edit functionality when true (optional) */
	export let hideEditButton: boolean = false;
	/** Hide assignment history when true (optional) */
	export let hideAssignmentsHistory: boolean = false;

	// Component state
	let loading = false;
	let error = '';
	let policiesData: CustomerPoliciesData | null = null;
	let filteredPolicies: Policy[] = [];
	let filteredClaims: Claim[] = [];

	// Polling service instance
	let pollingService: PollingService;

	// Data refresh state variables
	let refreshingPolicies = false;

	// Initial data loading state management
	let initialDataLoaded = false;
	let initialDataLoading = false;

	// Track current customer ID to reset flags when customer changes
	let currentCustomerId: number | null = null;

	// Modal states
	let policyDetailModalOpen = false;
	let selectedPolicy: Policy | null = null;
	let selectedClaim: Claim | null = null;

	// Filter states
	let showFilters = false;
	let searchQuery = '';
	let policyFilters: PolicyFilters = {
		status: [],
		type: [],
		search_query: ''
	};
	let claimFilters: ClaimFilters = {
		status: [],
		type: [],
		search_query: ''
	};

	// View state
	let activeView: 'policies' | 'claims' = 'policies';

	// Reactive language
	$: lang = get(language);

	/**
	 * Loads customer policies and claims data from API or fallback to mock data
	 * Handles API errors gracefully by falling back to mock data
	 * Updates component state with loaded data and initializes filtered arrays
	 *
	 * @throws {Error} When customer ID or access token is missing
	 */
	async function loadPoliciesData() {
		try {
			loading = true;
			error = '';

			if (!customer?.customer_id) {
				throw new Error('No customer ID provided');
			}

			if (!access_token) {
				throw new Error('No access token provided');
			}

			// Try to fetch from API first, fallback to mock data
			try {
				const result = await services.customers.getCustomerPoliciesAndClaims(
					customer.customer_id.toString(),
					access_token
				);

				if (result.res_status === 200 && result.customer_policies && result.customer_policies.length > 0) {
					// Convert API response to our expected format
					const apiData = result.customer_policies[0];
					policiesData = {
						customer_id: apiData.customer_id,
						customer_name: apiData.customer_name,
						customer_email: apiData.customer_email,
						policies: apiData.policies || [],
						claims: apiData.claims || [],
						statistics: apiData.statistics || {
							total_policies: 0,
							active_policies: 0,
							expired_policies: 0,
							pending_policies: 0,
							cancelled_policies: 0,
							waiting_period_policies: 0,
							nearly_expired_policies: 0,
							total_premium_amount: 0,
							total_coverage_amount: 0,
							average_premium: 0,
							total_claims: 0,
							active_claims: 0,
							approved_claims: 0,
							rejected_claims: 0,
							total_claims_amount: 0,
							total_paid_amount: 0,
							policy_type_breakdown: {
								'LIFE': 0,
								'HEALTH': 0,
								'AUTO': 0,
								'PROPERTY': 0,
								'TRAVEL': 0,
								'DISABILITY': 0,
								'CRITICAL_ILLNESS': 0
							},
							recent_policies: 0,
							recent_claims: 0
						},
						last_updated: new Date().toISOString()
					};
				} else {
					throw new Error(result.error_msg || 'Failed to load policies from API');
				}
			} catch (apiError) {
				console.warn('API call failed, using mock data:', apiError);
				// Fallback to mock data
				policiesData = getPoliciesByCustomerId(customer.customer_id);
			}

			// Initialize filtered data
			if (policiesData) {
				filteredPolicies = policiesData.policies || [];
				filteredClaims = policiesData.claims || [];
			}

		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to load policies data';
			console.error('Error loading policies:', err);
		} finally {
			loading = false;
		}
	}

	/**
	 * Applies current filter criteria to the policies array
	 * Filters by search query, status, and policy type
	 * Updates the filteredPolicies array with matching results
	 */
	function applyPolicyFilters() {
		if (!policiesData) return;

		filteredPolicies = policiesData.policies.filter(policy => {
			// Search filter
			if (searchQuery.trim()) {
				const query = searchQuery.toLowerCase();
				const matchesSearch = 
					policy.policy_number.toLowerCase().includes(query) ||
					policy.product.name.toLowerCase().includes(query) ||
					policy.product.product_type.toLowerCase().includes(query);
				if (!matchesSearch) return false;
			}

			// Status filter
			if (policyFilters.status && policyFilters.status.length > 0) {
				if (!policyFilters.status.includes(policy.policy_status)) return false;
			}

			// Type filter
			if (policyFilters.type && policyFilters.type.length > 0) {
				if (!policyFilters.type.includes(policy.product.product_type)) return false;
			}

			return true;
		});
	}

	// Filter claims based on current filters
	function applyClaimFilters() {
		if (!policiesData) return;

		filteredClaims = policiesData.claims.filter(claim => {
			// Search filter
			if (searchQuery.trim()) {
				const query = searchQuery.toLowerCase();
				const matchesSearch = 
					claim.claim_number.toLowerCase().includes(query) ||
					claim.description.toLowerCase().includes(query) ||
					claim.claim_type.toLowerCase().includes(query);
				if (!matchesSearch) return false;
			}

			// Status filter
			if (claimFilters.status && claimFilters.status.length > 0) {
				if (!claimFilters.status.includes(claim.claim_status)) return false;
			}

			// Type filter
			if (claimFilters.type && claimFilters.type.length > 0) {
				if (!claimFilters.type.includes(claim.claim_type)) return false;
			}

			return true;
		});
	}

	// Apply filters when search query or filters change
	$: if (searchQuery !== undefined) {
		if (activeView === 'policies') {
			applyPolicyFilters();
		} else {
			applyClaimFilters();
		}
	}

	/**
	 * Opens the policy detail modal with the selected policy
	 * Sets the selected policy and opens the modal for detailed view
	 *
	 * @param {Policy} policy - The policy object to display in detail
	 */
	function openPolicyDetail(policy: Policy) {
		selectedPolicy = policy;
		policyDetailModalOpen = true;
	}

	// Close policy detail modal
	function closePolicyDetail() {
		selectedPolicy = null;
		policyDetailModalOpen = false;
	}

	// Get status color class
	function getStatusColorClass(status: string): string {
		switch (status.toUpperCase()) {
			case 'ACTIVE':
				return 'text-green-600 bg-green-100';
			case 'EXPIRED':
				return 'text-red-600 bg-red-100';
			case 'PENDING':
				return 'text-yellow-600 bg-yellow-100';
			case 'CANCELLED':
				return 'text-gray-600 bg-gray-100';
			case 'APPROVED':
				return 'text-green-600 bg-green-100';
			case 'UNDER_REVIEW':
				return 'text-blue-600 bg-blue-100';
			case 'REJECTED':
				return 'text-red-600 bg-red-100';
			case 'PAID':
				return 'text-green-600 bg-green-100';
			default:
				return 'text-gray-600 bg-gray-100';
		}
	}

	// Format currency
	function formatCurrency(amount: number, currency: string = 'THB'): string {
		return new Intl.NumberFormat('th-TH', {
			style: 'currency',
			currency: currency
		}).format(amount);
	}

	// Format date
	function formatDate(dateString: string): string {
		return new Date(dateString).toLocaleDateString('th-TH', {
			year: 'numeric',
			month: 'short',
			day: 'numeric'
		});
	}

	// Initialize data loading and polling
	async function initializeDataAndPolling() {
		try {
			initialDataLoading = true;

			// Load initial data
			await loadPoliciesData();

			// Initialize polling after successful data load
			initializePolling();

			initialDataLoaded = true;
		} catch (err) {
			console.error('Failed to initialize policies data and polling:', err);
		} finally {
			initialDataLoading = false;
		}
	}

	// Initialize polling service
	function initializePolling() {
		pollingService = PollingService.getInstance();

		// Register policies polling
		const policiesConfig: PollingConfig = {
			endpoint: `/customer/api/customers/${customer.customer_id}/policies/`,
			interval: 30000, // 30 seconds
			customFetcher: () => loadPoliciesData(),
			onError: (error) => {
				console.error('Policies polling error:', error);
			}
		};
		pollingService.registerEndpoint('customer-policies', policiesConfig);
	}

	// Refresh policies data
	async function refreshPoliciesData() {
		try {
			refreshingPolicies = true;
			await loadPoliciesData();
		} catch (err) {
			console.error('Failed to refresh policies data:', err);
		} finally {
			refreshingPolicies = false;
		}
	}

	// Initialize component
	onMount(() => {
		// Only initialize if not already done and not in progress
		if (customer && customer.customer_id && access_token && !initialDataLoaded && !initialDataLoading) {
			initializeDataAndPolling();
		}
	});

	// Cleanup
	onDestroy(() => {
		if (pollingService) {
			// Clean up polling endpoints
			pollingService.unregisterEndpoint('customer-policies');
		}
		// Reset initialization flags for cleanup
		initialDataLoaded = false;
		initialDataLoading = false;
	});
</script>

<div id="policies-tab-container" class="p-6" data-testid="policies-tab">
	{#if loading}
		<div class="text-center py-8">
			<Spinner class="mx-auto" />
			<p class="mt-2 text-sm text-gray-500">{t('loading_policies')}</p>
		</div>
	{:else if error}
		<div class="text-center py-8">
			<ExclamationCircleOutline class="mx-auto h-12 w-12 text-red-400" />
			<h3 class="mt-2 text-sm font-medium text-gray-900">{t('error_loading_policies')}</h3>
			<p class="mt-1 text-sm text-gray-500">{error}</p>
			<Button 
				on:click={loadPoliciesData}
				class="mt-4"
				color="blue"
			>
				{t('try_again')}
			</Button>
		</div>
	{:else if !policiesData || (policiesData.policies.length === 0 && policiesData.claims.length === 0)}
		<div class="text-center text-gray-500 mt-8">
			<NewspaperOutline class="mx-auto h-12 w-12 text-gray-400" />
			<h3 class="mt-2 text-sm font-medium text-gray-900">{t('no_policies_available')}</h3>
			<p class="mt-1 text-sm text-gray-500">{t('no_policies_description')}</p>
		</div>
	{:else}
		<!-- Summary Statistics Cards -->
		<!-- <div id="policies-tab-summary-cards" class="grid grid-cols-2 gap-4 mb-6">
			<Card size="lg" class="p-4">
				<div class="flex items-center">
					<div class="p-2 rounded-full bg-blue-100 mr-3">
						<TicketSolid class="h-6 w-6 text-blue-700" />
					</div>
					<div>
						<p class="text-blue-500 text-sm">{t('total_policies')}</p>
						<p class="text-2xl font-bold">{policiesData.statistics.total_policies}</p>
					</div>
				</div>
			</Card>

			<Card class="p-4">
				<div class="flex items-center">
					<div class="p-2 rounded-full bg-green-100 mr-3">
						<ClipboardListSolid class="h-6 w-6 text-green-700" />
					</div>
					<div>
						<p class="text-green-500 text-sm">{t('active_policies')}</p>
						<p class="text-2xl font-bold">{policiesData.statistics.active_policies}</p>
					</div>
				</div>
			</Card>

			<Card class="p-4">
				<div class="flex items-center">
					<div class="p-2 rounded-full bg-yellow-100 mr-3">
						<CalendarMonthSolid class="h-6 w-6 text-yellow-700" />
					</div>
					<div>
						<p class="text-yellow-500 text-sm">{t('total_claims')}</p>
						<p class="text-2xl font-bold">{policiesData.statistics.total_claims}</p>
					</div>
				</div>
			</Card>

			<Card class="p-4">
				<div class="flex items-center">
					<div class="p-2 rounded-full bg-red-100 mr-3">
						<ClockSolid class="h-6 w-6 text-red-700" />
					</div>
					<div>
						<p class="text-red-500 text-sm">{t('active_claims')}</p>
						<p class="text-2xl font-bold">{policiesData.statistics.active_claims}</p>
					</div>
				</div>
			</Card>
		</div> -->

		<!-- View Toggle and Search/Filter Controls -->
		<div id="policies-tab-controls" class="mb-6">
			<!-- View Toggle -->
			<div class="flex items-center justify-between mb-4">
				<div class="flex space-x-1 bg-gray-100 rounded-lg p-1">
					<button
						id="policies-tab-view-policies"
						on:click={() => activeView = 'policies'}
						class="px-4 py-2 text-sm font-medium rounded-md transition-colors
							{activeView === 'policies' 
								? 'bg-white text-blue-600 shadow-sm' 
								: 'text-gray-500 hover:text-gray-700'}"
					>
						{t('policies')} ({policiesData.policies.length})
					</button>
					<button
						id="policies-tab-view-claims"
						on:click={() => activeView = 'claims'}
						class="px-4 py-2 text-sm font-medium rounded-md transition-colors
							{activeView === 'claims' 
								? 'bg-white text-blue-600 shadow-sm' 
								: 'text-gray-500 hover:text-gray-700'}"
					>
						{t('claims')} ({policiesData.claims.length})
					</button>
				</div>

				<!-- <Button
					id="policies-tab-toggle-filters"
					on:click={() => showFilters = !showFilters}
					color="light"
					size="sm"
				>
					<FilterSolid class="w-4 h-4 mr-2" />
					{t('filters')}
				</Button> -->
			</div>

			<!-- Search Bar -->
			<!-- <div class="relative mb-4">
				<SearchOutline class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
				<Input
					id="policies-tab-search"
					bind:value={searchQuery}
					placeholder={activeView === 'policies' ? t('search_policies') : t('search_claims')}
					class="pl-10"
				/>
			</div> -->

			<!-- Filter Panel (collapsible) -->
			{#if showFilters}
				<Card class="p-4 mb-4">
					<h4 class="text-sm font-medium text-gray-900 mb-3">{t('filter_options')}</h4>
					<div class="grid grid-cols-1 md:grid-cols-3 gap-4">
						{#if activeView === 'policies'}
							<!-- Policy Status Filter -->
							<div>
								<label for="policies-tab-status-filter" class="block text-xs font-medium text-gray-700 mb-1">{t('status')}</label>
								<Select
									id="policies-tab-status-filter"
									bind:value={policyFilters.status}
									multiple
									placeholder={t('select_status')}
									size="sm"
								>
									<option value="ACTIVE">{t('active')}</option>
									<option value="EXPIRED">{t('expired')}</option>
									<option value="PENDING">{t('pending')}</option>
									<option value="CANCELLED">{t('cancelled')}</option>
								</Select>
							</div>

							<!-- Policy Type Filter -->
							<div>
								<label for="policies-tab-type-filter" class="block text-xs font-medium text-gray-700 mb-1">{t('policy_type')}</label>
								<Select
									id="policies-tab-type-filter"
									bind:value={policyFilters.type}
									multiple
									placeholder={t('select_type')}
									size="sm"
								>
									<option value="LIFE">{t('life_insurance')}</option>
									<option value="HEALTH">{t('health_insurance')}</option>
									<option value="AUTO">{t('auto_insurance')}</option>
									<option value="PROPERTY">{t('property_insurance')}</option>
								</Select>
							</div>
						{:else}
							<!-- Claim Status Filter -->
							<div>
								<label for="policies-tab-claim-status-filter" class="block text-xs font-medium text-gray-700 mb-1">{t('status')}</label>
								<Select
									id="policies-tab-claim-status-filter"
									bind:value={claimFilters.status}
									multiple
									placeholder={t('select_status')}
									size="sm"
								>
									<option value="SUBMITTED">{t('submitted')}</option>
									<option value="UNDER_REVIEW">{t('under_review')}</option>
									<option value="APPROVED">{t('approved')}</option>
									<option value="REJECTED">{t('rejected')}</option>
									<option value="PAID">{t('paid')}</option>
								</Select>
							</div>

							<!-- Claim Type Filter -->
							<div>
								<label for="policies-tab-claim-type-filter" class="block text-xs font-medium text-gray-700 mb-1">{t('claim_type')}</label>
								<Select
									id="policies-tab-claim-type-filter"
									bind:value={claimFilters.type}
									multiple
									placeholder={t('select_type')}
									size="sm"
								>
									<option value="MEDICAL">{t('medical')}</option>
									<option value="ACCIDENT">{t('accident')}</option>
									<option value="HOSPITALIZATION">{t('hospitalization')}</option>
									<option value="PROPERTY_DAMAGE">{t('property_damage')}</option>
								</Select>
							</div>
						{/if}

						<!-- Clear Filters Button -->
						<div class="flex items-end">
							<Button
								id="policies-tab-clear-filters"
								on:click={() => {
									searchQuery = '';
									policyFilters = { status: [], type: [], search_query: '' };
									claimFilters = { status: [], type: [], search_query: '' };
								}}
								color="light"
								size="sm"
								class="w-full"
							>
								{t('clear_filters')}
							</Button>
						</div>
					</div>
				</Card>
			{/if}
		</div>

		<!-- Content Display -->
		{#if activeView === 'policies'}
			<!-- Policies Grid -->
			<div id="policies-tab-policies-grid" class="grid grid-cols-1 gap-6">
				{#each filteredPolicies as policy}
					<Card size="lg" class="p-6 hover:shadow-lg transition-shadow cursor-pointer" on:click={() => openPolicyDetail(policy)}>
						<div class="flex justify-between items-start mb-4">
							<div>
								<h3 class="text-lg font-semibold text-gray-900">{policy.product.name}</h3>
								<p class="text-sm text-gray-500">{policy.policy_number}</p>
							</div>
							<Badge class={getStatusColorClass(policy.policy_status)}>
								{t(policy.policy_status.toLowerCase())}
							</Badge>
						</div>

						<div class="space-y-3 text-sm">
							<!-- Policy Type -->
							<div class="flex justify-between">
								<span class="text-gray-500">{t('type')}</span>
								<span class="font-medium">{t(policy.product.product_type.toLowerCase())}</span>
							</div>

							<!-- Coverage Amount -->
							<div class="flex justify-between">
								<span class="text-gray-500">{t('coverage')}</span>
								<span class="font-medium">{formatCurrency(policy.coverage_amount, policy.currency)}</span>
							</div>

							<!-- Premium -->
							<div class="flex justify-between">
								<span class="text-gray-500">{t('premium')}</span>
								<span class="font-medium">{formatCurrency(policy.premium_amount, policy.currency)}</span>
							</div>

							<!-- Expiry Date -->
							<div class="flex justify-between">
								<span class="text-gray-500">{t('expires')}</span>
								<span class="font-medium">{formatDate(policy.end_date)}</span>
							</div>

							<!-- Claims Summary -->
							{#if policy.total_claims && policy.total_claims > 0}
								<div class="flex justify-between border-t pt-2">
									<span class="text-gray-500">{t('claims')}</span>
									<span class="font-medium text-blue-600">
										{policy.total_claims} ({policy.active_claims} {t('active')})
									</span>
								</div>
							{/if}
						</div>

						<div class="mt-4 flex justify-end">
							<Button size="xs" color="blue" class="flex items-center">
								<EyeOutline class="w-3 h-3 mr-1" />
								{t('view_details')}
							</Button>
						</div>
					</Card>
				{/each}
			</div>

			{#if filteredPolicies.length === 0}
				<div class="text-center text-gray-500 mt-8">
					<ClipboardListSolid class="mx-auto h-8 w-8 text-gray-400" />
					<p class="mt-2 text-sm">{t('no_policies_match_filter')}</p>
				</div>
			{/if}
		{:else}
			<!-- Claims List -->
			<div id="policies-tab-claims-list" class="space-y-4">
				{#each filteredClaims as claim}
					<Card size="lg" class="p-6">
						<div class="flex justify-between items-start mb-4">
							<div>
								<h3 class="text-lg font-semibold text-gray-900">{claim.claim_number}</h3>
								<p class="text-sm text-gray-500">{t('policy')}: {claim.policy_number}</p>
							</div>
							<Badge class={getStatusColorClass(claim.claim_status)}>
								{t(claim.claim_status.toLowerCase())}
							</Badge>
						</div>

						<div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
							<div class="space-y-2">
								<div class="flex justify-between">
									<span class="text-gray-500">{t('claim_type')}</span>
									<span class="font-medium">{t(claim.claim_type.toLowerCase())}</span>
								</div>
								<div class="flex justify-between">
									<span class="text-gray-500">{t('incident_date')}</span>
									<span class="font-medium">{formatDate(claim.incident_date)}</span>
								</div>
								<div class="flex justify-between">
									<span class="text-gray-500">{t('reported_date')}</span>
									<span class="font-medium">{formatDate(claim.reported_date)}</span>
								</div>
							</div>
							<div class="space-y-2">
								<div class="flex justify-between">
									<span class="text-gray-500">{t('claimed_amount')}</span>
									<span class="font-medium">{formatCurrency(claim.claimed_amount, claim.currency)}</span>
								</div>
								{#if claim.approved_amount}
									<div class="flex justify-between">
										<span class="text-gray-500">{t('approved_amount')}</span>
										<span class="font-medium text-green-600">{formatCurrency(claim.approved_amount, claim.currency)}</span>
									</div>
								{/if}
								{#if claim.assigned_adjuster}
									<div class="flex justify-between">
										<span class="text-gray-500">{t('adjuster')}</span>
										<span class="font-medium">{claim.assigned_adjuster}</span>
									</div>
								{/if}
							</div>
						</div>

						{#if claim.description}
							<div class="mt-4 p-3 bg-gray-50 rounded-lg">
								<p class="text-sm text-gray-700">{claim.description}</p>
							</div>
						{/if}

						<div class="mt-4 flex justify-end">
							<Button size="xs" color="blue" class="flex items-center">
								<EyeOutline class="w-3 h-3 mr-1" />
								{t('view_details')}
							</Button>
						</div>
					</Card>
				{/each}
			</div>

			{#if filteredClaims.length === 0}
				<div class="text-center text-gray-500 mt-8">
					<ClipboardListSolid class="mx-auto h-8 w-8 text-gray-400" />
					<p class="mt-2 text-sm">{t('no_claims_match_filter')}</p>
				</div>
			{/if}
		{/if}
	{/if}
</div>

<!-- Policy Detail Modal -->
<Modal bind:open={policyDetailModalOpen} size="xl" title={selectedPolicy?.product.name || t('policy_details')}>
	{#if selectedPolicy}
		<div class="space-y-6">
			<!-- Policy Header -->
			<div class="flex justify-between items-start">
				<div>
					<h2 class="text-xl font-semibold text-gray-900">{selectedPolicy.product.name}</h2>
					<p class="text-gray-500">{selectedPolicy.policy_number}</p>
				</div>
				<Badge class={getStatusColorClass(selectedPolicy.policy_status)}>
					{t(selectedPolicy.policy_status.toLowerCase())}
				</Badge>
			</div>

			<!-- Policy Details Grid -->
			<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
				<div class="space-y-4">
					<h3 class="text-lg font-medium text-gray-900">{t('policy_information')}</h3>
					<div class="space-y-3 text-sm">
						<div class="flex justify-between">
							<span class="text-gray-500">{t('product_type')}</span>
							<span class="font-medium">{t(selectedPolicy.product.product_type.toLowerCase())}</span>
						</div>
						<div class="flex justify-between">
							<span class="text-gray-500">{t('issue_date')}</span>
							<span class="font-medium">{formatDate(selectedPolicy.issue_date)}</span>
						</div>
						<div class="flex justify-between">
							<span class="text-gray-500">{t('start_date')}</span>
							<span class="font-medium">{formatDate(selectedPolicy.start_date)}</span>
						</div>
						<div class="flex justify-between">
							<span class="text-gray-500">{t('end_date')}</span>
							<span class="font-medium">{formatDate(selectedPolicy.end_date)}</span>
						</div>
						<div class="flex justify-between">
							<span class="text-gray-500">{t('payment_frequency')}</span>
							<span class="font-medium">{t(selectedPolicy.payment_frequency.toLowerCase())}</span>
						</div>
					</div>
				</div>

				<div class="space-y-4">
					<h3 class="text-lg font-medium text-gray-900">{t('financial_details')}</h3>
					<div class="space-y-3 text-sm">
						<div class="flex justify-between">
							<span class="text-gray-500">{t('coverage_amount')}</span>
							<span class="font-medium text-green-600">{formatCurrency(selectedPolicy.coverage_amount, selectedPolicy.currency)}</span>
						</div>
						<div class="flex justify-between">
							<span class="text-gray-500">{t('premium_amount')}</span>
							<span class="font-medium">{formatCurrency(selectedPolicy.premium_amount, selectedPolicy.currency)}</span>
						</div>
						{#if selectedPolicy.deductible}
							<div class="flex justify-between">
								<span class="text-gray-500">{t('deductible')}</span>
								<span class="font-medium">{formatCurrency(selectedPolicy.deductible, selectedPolicy.currency)}</span>
							</div>
						{/if}
						{#if selectedPolicy.next_payment_date}
							<div class="flex justify-between">
								<span class="text-gray-500">{t('next_payment')}</span>
								<span class="font-medium">{formatDate(selectedPolicy.next_payment_date)}</span>
							</div>
						{/if}
					</div>
				</div>
			</div>

			<!-- Claims Summary for this Policy -->
			{#if selectedPolicy.total_claims && selectedPolicy.total_claims > 0}
				<div>
					<h3 class="text-lg font-medium text-gray-900 mb-4">{t('claims_history')}</h3>
					<div class="bg-gray-50 rounded-lg p-4">
						<div class="grid grid-cols-3 gap-4 text-center">
							<div>
								<p class="text-2xl font-bold text-gray-900">{selectedPolicy.total_claims}</p>
								<p class="text-sm text-gray-500">{t('total_claims')}</p>
							</div>
							<div>
								<p class="text-2xl font-bold text-blue-600">{selectedPolicy.active_claims}</p>
								<p class="text-sm text-gray-500">{t('active_claims')}</p>
							</div>
							<div>
								<p class="text-2xl font-bold text-green-600">{formatCurrency(selectedPolicy.total_claims_amount || 0, selectedPolicy.currency)}</p>
								<p class="text-sm text-gray-500">{t('total_claimed')}</p>
							</div>
						</div>
					</div>
				</div>
			{/if}

			<!-- Beneficiaries -->
			{#if selectedPolicy.beneficiaries && selectedPolicy.beneficiaries.length > 0}
				<div>
					<h3 class="text-lg font-medium text-gray-900 mb-4">{t('beneficiaries')}</h3>
					<div class="space-y-3">
						{#each selectedPolicy.beneficiaries as beneficiary}
							<div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
								<div>
									<p class="font-medium text-gray-900">{beneficiary.name}</p>
									<p class="text-sm text-gray-500">{beneficiary.relationship}</p>
								</div>
								<div class="text-right">
									<p class="font-medium text-gray-900">{beneficiary.percentage}%</p>
									{#if beneficiary.contact_info?.phone}
										<p class="text-sm text-gray-500">{beneficiary.contact_info.phone}</p>
									{/if}
								</div>
							</div>
						{/each}
					</div>
				</div>
			{/if}
		</div>
	{/if}
</Modal>
